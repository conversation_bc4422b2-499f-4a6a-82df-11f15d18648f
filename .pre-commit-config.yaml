repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: trailing-whitespace
      - id: end-of-file-fixer

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
      - id: pyupgrade
        args: [--py37-plus]

  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black

  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        additional_dependencies:
          - prettier@3.5.2

  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
      - id: codespell
        args:
          - -L
          - "ro"

  - repo: local
    hooks:
      - id: shfmt
        name: shfmt
        language: golang
        additional_dependencies: [mvdan.cc/sh/v3/cmd/shfmt@latest]
        entry: shfmt
        args: [-w]
        types: [shell]

  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck
        args: [-x]
