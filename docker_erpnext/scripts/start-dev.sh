#!/bin/bash
# ERPNext 开发环境启动脚本

set -e

echo "=== ERPNext 开发环境启动 ==="

# 等待数据库服务启动
echo "等待数据库服务启动..."
wait-for-it ${DB_HOST:-mariadb}:${DB_PORT:-3306} -t 60

# 等待 Redis 服务启动
echo "等待 Redis 服务启动..."
wait-for-it redis-cache:6379 -t 30
wait-for-it redis-queue:6379 -t 30

# 检查是否已经初始化过 bench
if [ ! -f "/home/<USER>/frappe-bench/sites/common_site_config.json" ]; then
    echo "初始化 Frappe Bench..."
    
    # 如果挂载了本地源码，使用本地源码
    if [ -d "/home/<USER>/frappe-bench/apps/frappe" ] && [ -d "/home/<USER>/frappe-bench/apps/erpnext" ]; then
        echo "检测到本地源码挂载，跳过源码下载..."
        
        # 创建基本的 bench 结构
        mkdir -p sites logs
        
        # 创建 Python 虚拟环境
        python3 -m venv env
        
        # 激活虚拟环境并安装依赖
        source env/bin/activate
        
        # 安装 Frappe
        echo "安装 Frappe Framework..."
        pip install -e apps/frappe
        
        # 安装 ERPNext
        echo "安装 ERPNext..."
        pip install -e apps/erpnext
        
        # 安装其他依赖
        if [ -f "apps/frappe/requirements.txt" ]; then
            pip install -r apps/frappe/requirements.txt
        fi
        
        if [ -f "apps/erpnext/requirements.txt" ]; then
            pip install -r apps/erpnext/requirements.txt
        fi
        
    else
        echo "从远程仓库初始化 bench..."
        # 使用标准的 bench init 流程
        bench init \
            --frappe-branch=${FRAPPE_BRANCH:-version-15} \
            --no-procfile \
            --no-backups \
            --skip-redis-config-generation \
            --verbose \
            .
        
        # 获取 ERPNext 应用
        bench get-app --branch=${ERPNEXT_BRANCH:-version-15} --resolve-deps erpnext https://github.com/frappe/erpnext
    fi
    
    # 创建基本配置
    echo "{}" > sites/common_site_config.json
    
    # 配置数据库和 Redis
    bench set-config -g db_host ${DB_HOST:-mariadb}
    bench set-config -gp db_port ${DB_PORT:-3306}
    bench set-config -g redis_cache "redis://redis-cache:6379"
    bench set-config -g redis_queue "redis://redis-queue:6379"
    bench set-config -g redis_socketio "redis://redis-queue:6379"
    bench set-config -gp socketio_port 9000
    
    # 设置开发者模式
    if [ "${DEVELOPER_MODE:-1}" = "1" ]; then
        bench set-config -gp developer_mode 1
    fi
    
    echo "Bench 初始化完成"
fi

# 检查是否需要创建站点
if [ ! -d "/home/<USER>/frappe-bench/sites/${SITE_NAME:-development.localhost}" ]; then
    echo "创建站点: ${SITE_NAME:-development.localhost}"
    
    # 创建新站点
    bench new-site \
        --db-root-username=root \
        --db-host=${DB_HOST:-mariadb} \
        --db-root-password=${DB_ROOT_PASSWORD:-123} \
        --admin-password=${ADMIN_PASSWORD:-admin} \
        --install-app=erpnext \
        ${SITE_NAME:-development.localhost}
    
    echo "站点创建完成"
fi

# 构建前端资源
echo "构建前端资源..."
bench build

# 启动服务
echo "启动 ERPNext 服务..."

# 根据启动模式选择不同的启动方式
if [ "${START_MODE:-gunicorn}" = "development" ]; then
    # 开发模式：启动开发服务器
    echo "以开发模式启动..."
    bench start
else
    # 生产模式：使用 Gunicorn
    echo "以生产模式启动..."
    exec /home/<USER>/frappe-bench/env/bin/gunicorn \
        --chdir=/home/<USER>/frappe-bench/sites \
        --bind=0.0.0.0:8000 \
        --threads=4 \
        --workers=2 \
        --worker-class=gthread \
        --worker-tmp-dir=/dev/shm \
        --timeout=120 \
        --preload \
        frappe.app:application
fi
