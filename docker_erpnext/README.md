# ERPNext 本地开发 Docker 环境

这是一个简化的 ERPNext Docker 构建方案，专为本地开发设计。基于官方 frappe_docker 项目的构建流程，但简化了复杂的 CI/CD 配置，支持直接从本地源码构建和运行。

## 功能特性

- ✅ **本地源码支持**: 支持挂载本地 ERPNext 和 Frappe 源码进行开发
- ✅ **完整服务栈**: 包含 MariaDB、Redis、Nginx、WebSocket、队列服务
- ✅ **开发友好**: 支持热重载、调试模式、开发者模式
- ✅ **一键启动**: 使用 `docker-compose up -d` 即可启动完整环境
- ✅ **真实构建**: 保持官方构建流程的核心逻辑，确保构建质量

## 目录结构

```
docker_erpnext/
├── .env                    # 环境变量配置
├── Dockerfile              # 简化的多阶段构建文件
├── docker-compose.yml      # 服务编排配置
├── scripts/
│   └── start-dev.sh        # 开发环境启动脚本
└── README.md               # 使用说明
```

## 快速开始

### 1. 准备源码

确保你的本地有 ERPNext 开发源码：

```bash
# 如果还没有源码，先克隆
git clone https://github.com/frappe/frappe.git /home/<USER>/frappe_dev
git clone https://github.com/frappe/erpnext.git /home/<USER>/erpnext_dev

# 切换到需要的分支
cd /home/<USER>/frappe_dev && git checkout version-15
cd /home/<USER>/erpnext_dev && git checkout version-15
```

### 2. 配置环境变量

编辑 `.env` 文件，确保源码路径正确：

```bash
# 修改为你的实际源码路径
ERPNEXT_SOURCE_PATH=/home/<USER>/erpnext_dev
FRAPPE_SOURCE_PATH=/home/<USER>/frappe_dev
```

### 3. 启动服务

```bash
# 进入项目目录
cd docker_erpnext

# 构建并启动所有服务
docker-compose up -d

# 查看启动日志
docker-compose logs -f erpnext
```

### 4. 访问应用

- **ERPNext 应用**: http://localhost:8080
- **默认登录**: 
  - 用户名: `Administrator`
  - 密码: `admin`

## 服务说明

### 核心服务

- **erpnext**: 主应用服务，运行 Gunicorn WSGI 服务器
- **nginx**: 前端代理，处理静态文件和反向代理
- **websocket**: WebSocket 服务，处理实时通信
- **queue-short**: 短任务队列处理器
- **queue-long**: 长任务队列处理器  
- **scheduler**: 定时任务调度器

### 基础服务

- **mariadb**: MySQL 兼容数据库
- **redis-cache**: Redis 缓存服务
- **redis-queue**: Redis 队列服务

## 开发模式

### 使用本地源码

当 `.env` 中配置了正确的源码路径时，容器会自动挂载本地源码：

```bash
# 本地修改源码后，重启服务即可生效
docker-compose restart erpnext

# 或者进入容器手动重新构建前端资源
docker-compose exec erpnext bash
bench build
```

### 调试模式

设置环境变量启用调试：

```bash
# 在 .env 中设置
DEBUG_MODE=1
DEVELOPER_MODE=1
START_MODE=development  # 使用开发服务器而非 Gunicorn
```

### 进入容器调试

```bash
# 进入主应用容器
docker-compose exec erpnext bash

# 进入 bench 目录
cd /home/<USER>/frappe-bench

# 运行 bench 命令
bench --help
bench console
bench migrate
```

## 常用命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart erpnext

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f erpnext
```

### 数据管理

```bash
# 备份数据库
docker-compose exec mariadb mysqldump -u root -p123 erpnext > backup.sql

# 清理并重新构建
docker-compose down -v  # 删除数据卷
docker-compose build --no-cache
docker-compose up -d
```

### 应用管理

```bash
# 进入应用容器
docker-compose exec erpnext bash

# 创建新站点
bench new-site mysite.localhost --admin-password admin

# 安装应用
bench --site mysite.localhost install-app erpnext

# 迁移数据库
bench --site mysite.localhost migrate

# 构建前端资源
bench build
```

## 故障排除

### 常见问题

1. **端口冲突**: 修改 `.env` 中的端口配置
2. **源码路径错误**: 检查 `.env` 中的路径是否存在
3. **权限问题**: 确保源码目录对 Docker 可读
4. **数据库连接失败**: 等待数据库服务完全启动

### 重置环境

```bash
# 完全清理环境
docker-compose down -v --remove-orphans
docker system prune -f
docker-compose up -d --build
```

## 与官方构建的差异

### 简化内容

- 移除了 GitHub Actions CI/CD 配置
- 移除了多架构构建支持
- 移除了 Docker Bake 复杂配置
- 简化了测试流程

### 保留内容

- 完整的多阶段 Dockerfile 构建逻辑
- 真实的 bench init 和 get-app 流程
- 完整的服务架构 (nginx, websocket, queues, scheduler)
- 生产级别的配置和优化

这个方案在保持构建质量的同时，大大简化了本地开发的复杂度，让你可以专注于 ERPNext 的开发工作。
