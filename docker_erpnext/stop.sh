#!/bin/bash
# ERPNext 本地开发环境停止脚本

set -e

echo "=== ERPNext 本地开发环境停止脚本 ==="

# 检查是否有运行的服务
if docker-compose ps -q | grep -q .; then
    echo "停止所有服务..."
    docker-compose down
    
    echo "✅ 所有服务已停止"
else
    echo "ℹ️  没有运行的服务"
fi

# 可选：清理数据卷
read -p "是否要删除所有数据 (包括数据库)? [y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "删除数据卷..."
    docker-compose down -v
    echo "✅ 数据卷已删除"
fi

# 可选：清理镜像
read -p "是否要删除构建的镜像? [y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "删除镜像..."
    docker-compose down --rmi all
    echo "✅ 镜像已删除"
fi

echo "🏁 ERPNext 开发环境已完全停止"
