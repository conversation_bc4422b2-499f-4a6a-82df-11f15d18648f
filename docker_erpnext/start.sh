#!/bin/bash
# ERPNext 本地开发环境快速启动脚本

set -e

echo "=== ERPNext 本地开发环境启动脚本 ==="

# 检查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装或未在 PATH 中"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装或未在 PATH 中"
    exit 1
fi

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo "错误: .env 文件不存在"
    exit 1
fi

# 加载环境变量
source .env

echo "检查源码路径..."

# 检查源码路径
if [ -n "$ERPNEXT_SOURCE_PATH" ] && [ -d "$ERPNEXT_SOURCE_PATH" ]; then
    echo "✅ ERPNext 源码路径: $ERPNEXT_SOURCE_PATH"
else
    echo "⚠️  ERPNext 源码路径不存在，将从远程仓库下载"
fi

if [ -n "$FRAPPE_SOURCE_PATH" ] && [ -d "$FRAPPE_SOURCE_PATH" ]; then
    echo "✅ Frappe 源码路径: $FRAPPE_SOURCE_PATH"
else
    echo "⚠️  Frappe 源码路径不存在，将从远程仓库下载"
fi

# 检查端口占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用"
        return 1
    else
        echo "✅ 端口 $port 可用"
        return 0
    fi
}

echo "检查端口占用..."
check_port ${HTTP_PORT:-8080}
check_port 3306
check_port 6379

# 构建和启动服务
echo "构建 Docker 镜像..."
docker-compose build

echo "启动服务..."
docker-compose up -d

echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 等待 ERPNext 服务就绪
echo "等待 ERPNext 服务就绪..."
timeout=300
counter=0

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:${HTTP_PORT:-8080} > /dev/null 2>&1; then
        echo "✅ ERPNext 服务已就绪!"
        break
    fi
    
    echo "等待中... ($counter/$timeout)"
    sleep 5
    counter=$((counter + 5))
done

if [ $counter -ge $timeout ]; then
    echo "❌ ERPNext 服务启动超时"
    echo "查看日志:"
    docker-compose logs erpnext
    exit 1
fi

echo ""
echo "🎉 ERPNext 开发环境启动成功!"
echo ""
echo "访问地址:"
echo "  ERPNext: http://localhost:${HTTP_PORT:-8080}"
echo "  默认用户: Administrator"
echo "  默认密码: ${ADMIN_PASSWORD:-admin}"
echo ""
echo "常用命令:"
echo "  查看日志: docker-compose logs -f erpnext"
echo "  进入容器: docker-compose exec erpnext bash"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart erpnext"
echo ""
