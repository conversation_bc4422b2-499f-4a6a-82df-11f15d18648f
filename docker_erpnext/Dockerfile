# ERPNext 本地开发构建 Dockerfile
# 基于官方构建流程简化，支持本地源码挂载

ARG PYTHON_VERSION=3.11.6
ARG DEBIAN_BASE=bookworm
FROM python:${PYTHON_VERSION}-slim-${DEBIAN_BASE} AS base

ARG WKHTMLTOPDF_VERSION=0.12.6.1-3
ARG WKHTMLTOPDF_DISTRO=bookworm
ARG NODE_VERSION=20.19.2
ENV NVM_DIR=/home/<USER>/.nvm
ENV PATH=${NVM_DIR}/versions/node/v${NODE_VERSION}/bin/:${PATH}

# 创建 frappe 用户
RUN useradd -ms /bin/bash frappe

# 安装系统依赖
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
    curl \
    git \
    vim \
    nginx \
    gettext-base \
    file \
    # weasyprint dependencies
    libpango-1.0-0 \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    libpangocairo-1.0-0 \
    # For backups
    restic \
    gpg \
    # MariaDB
    mariadb-client \
    less \
    # Postgres
    libpq-dev \
    postgresql-client \
    # For healthcheck
    wait-for-it \
    jq \
    # 编译依赖 (合并到 base 阶段以简化)
    wget \
    libffi-dev \
    liblcms2-dev \
    libldap2-dev \
    libmariadb-dev \
    libsasl2-dev \
    libtiff5-dev \
    libwebp-dev \
    pkg-config \
    redis-tools \
    rlwrap \
    tk8.6-dev \
    cron \
    gcc \
    build-essential \
    libbz2-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js
RUN mkdir -p ${NVM_DIR} \
    && curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash \
    && . ${NVM_DIR}/nvm.sh \
    && nvm install ${NODE_VERSION} \
    && nvm use v${NODE_VERSION} \
    && npm install -g yarn \
    && nvm alias default v${NODE_VERSION} \
    && rm -rf ${NVM_DIR}/.cache \
    && echo 'export NVM_DIR="/home/<USER>/.nvm"' >>/home/<USER>/.bashrc \
    && echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm' >>/home/<USER>/.bashrc \
    && echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion' >>/home/<USER>/.bashrc

# 安装 wkhtmltopdf
RUN if [ "$(uname -m)" = "aarch64" ]; then export ARCH=arm64; fi \
    && if [ "$(uname -m)" = "x86_64" ]; then export ARCH=amd64; fi \
    && downloaded_file=wkhtmltox_${WKHTMLTOPDF_VERSION}.${WKHTMLTOPDF_DISTRO}_${ARCH}.deb \
    && curl -sLO https://github.com/wkhtmltopdf/packaging/releases/download/$WKHTMLTOPDF_VERSION/$downloaded_file \
    && apt-get update && apt-get install -y ./$downloaded_file \
    && rm $downloaded_file \
    && rm -rf /var/lib/apt/lists/*

# 安装 frappe-bench
RUN pip3 install frappe-bench

# 配置 nginx
RUN rm -fr /etc/nginx/sites-enabled/default \
    && sed -i '/user www-data/d' /etc/nginx/nginx.conf \
    && ln -sf /dev/stdout /var/log/nginx/access.log && ln -sf /dev/stderr /var/log/nginx/error.log \
    && touch /run/nginx.pid \
    && chown -R frappe:frappe /etc/nginx/conf.d \
    && chown -R frappe:frappe /etc/nginx/nginx.conf \
    && chown -R frappe:frappe /var/log/nginx \
    && chown -R frappe:frappe /var/lib/nginx \
    && chown -R frappe:frappe /run/nginx.pid

# 复制 nginx 配置模板和启动脚本
COPY resources/nginx-template.conf /templates/nginx/frappe.conf.template
COPY resources/nginx-entrypoint.sh /usr/local/bin/nginx-entrypoint.sh

# 切换到 frappe 用户
USER frappe
WORKDIR /home/<USER>

# 开发阶段 - 使用本地源码构建
FROM base AS development

ARG FRAPPE_BRANCH=version-15
ARG ERPNEXT_BRANCH=version-15

# 创建 bench 目录
RUN mkdir -p /home/<USER>/frappe-bench

# 设置工作目录
WORKDIR /home/<USER>/frappe-bench

# 创建启动脚本
COPY --chown=frappe:frappe docker_erpnext/scripts/start-dev.sh /home/<USER>/start-dev.sh
RUN chmod +x /home/<USER>/start-dev.sh

# 暴露端口
EXPOSE 8000 9000

# 卷挂载点
VOLUME [ \
  "/home/<USER>/frappe-bench/sites", \
  "/home/<USER>/frappe-bench/sites/assets", \
  "/home/<USER>/frappe-bench/logs", \
  "/home/<USER>/frappe-bench/apps" \
]

# 默认命令
CMD ["/home/<USER>/start-dev.sh"]
