# ERPNext 本地开发环境使用指南

## 🚀 快速开始

### 方式一：使用 Makefile (推荐)

```bash
cd docker_erpnext

# 查看所有可用命令
make help

# 一键启动 (构建 + 启动)
make start

# 查看服务状态
make status

# 查看日志
make logs
```

### 方式二：使用脚本

```bash
cd docker_erpnext

# 启动环境
./start.sh

# 停止环境
./stop.sh
```

### 方式三：使用 Docker Compose

```bash
cd docker_erpnext

# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f erpnext

# 停止服务
docker-compose down
```

## 📁 文件说明

```
docker_erpnext/
├── .env                    # 环境变量配置文件
├── Dockerfile              # 简化的多阶段构建文件
├── docker-compose.yml      # Docker Compose 服务编排
├── Makefile                # 便捷命令集合
├── README.md               # 详细文档
├── USAGE.md                # 使用指南 (本文件)
├── start.sh                # 启动脚本
├── stop.sh                 # 停止脚本
├── scripts/
│   └── start-dev.sh        # 容器内启动脚本
└── empty/                  # 空目录 (处理源码路径不存在)
```

## ⚙️ 配置说明

### 环境变量 (.env)

关键配置项：

```bash
# 源码路径 - 修改为你的实际路径
ERPNEXT_SOURCE_PATH=/home/<USER>/erpnext_dev
FRAPPE_SOURCE_PATH=/home/<USER>/frappe_dev

# 服务端口
HTTP_PORT=8080              # ERPNext Web 端口
SOCKETIO_PORT=9000          # WebSocket 端口

# 数据库配置
DB_PASSWORD=123
ADMIN_PASSWORD=admin

# 开发模式
DEVELOPER_MODE=1
DEBUG_MODE=1
```

### 源码准备

如果你有本地源码：

```bash
# 确保源码路径存在并且是正确的分支
ls -la /home/<USER>/erpnext_dev
ls -la /home/<USER>/frappe_dev

# 检查分支
cd /home/<USER>/erpnext_dev && git branch
cd /home/<USER>/frappe_dev && git branch
```

如果没有本地源码，系统会自动从 GitHub 下载。

## 🔧 常用操作

### 开发调试

```bash
# 进入主容器
make shell
# 或
docker-compose exec erpnext bash

# 进入 bench 目录
cd /home/<USER>/frappe-bench

# 运行 bench 命令
bench --help
bench console
bench migrate
bench build
```

### 数据库操作

```bash
# 进入数据库容器
docker-compose exec mariadb mysql -u root -p123

# 备份数据库
docker-compose exec mariadb mysqldump -u root -p123 erpnext > backup.sql

# 恢复数据库
docker-compose exec -T mariadb mysql -u root -p123 erpnext < backup.sql
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs -f erpnext
docker-compose logs -f nginx
docker-compose logs -f mariadb

# 查看最近的日志
docker-compose logs --tail=100 erpnext
```

### 服务管理

```bash
# 重启特定服务
docker-compose restart erpnext
docker-compose restart nginx

# 停止特定服务
docker-compose stop erpnext

# 启动特定服务
docker-compose start erpnext

# 查看服务状态
docker-compose ps
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 修改 .env 中的端口
   HTTP_PORT=8081
   SOCKETIO_PORT=9001
   ```

2. **源码路径不存在**
   ```bash
   # 检查路径是否正确
   ls -la /home/<USER>/erpnext_dev
   
   # 或者注释掉让系统自动下载
   # ERPNEXT_SOURCE_PATH=/home/<USER>/erpnext_dev
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs erpnext
   
   # 重新构建
   docker-compose build --no-cache
   ```

4. **数据库连接失败**
   ```bash
   # 等待数据库完全启动
   docker-compose logs mariadb
   
   # 重启数据库服务
   docker-compose restart mariadb
   ```

### 完全重置

```bash
# 停止所有服务并删除数据
make reset

# 或者手动执行
docker-compose down -v --remove-orphans
docker system prune -f
```

## 📊 性能优化

### 资源配置

在 `docker-compose.yml` 中可以添加资源限制：

```yaml
services:
  erpnext:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### 缓存优化

```bash
# 清理 Docker 缓存
docker system prune -a

# 使用 BuildKit 加速构建
export DOCKER_BUILDKIT=1
docker-compose build
```

## 🔗 访问地址

启动成功后可以访问：

- **ERPNext 主应用**: http://localhost:8080
- **数据库**: localhost:3306 (用户: root, 密码: 123)
- **Redis 缓存**: localhost:6379
- **WebSocket**: localhost:9000

默认登录信息：
- 用户名: `Administrator`
- 密码: `admin`

## 📝 开发工作流

1. **启动环境**: `make start`
2. **修改代码**: 在本地源码目录修改
3. **重启服务**: `make restart` 或 `docker-compose restart erpnext`
4. **查看效果**: 访问 http://localhost:8080
5. **调试问题**: `make logs` 或 `make shell`
6. **停止环境**: `make down`

这个环境完全模拟了官方的构建流程，但简化了复杂的 CI/CD 配置，非常适合本地开发和调试。
