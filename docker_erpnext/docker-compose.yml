version: '3.8'

# ERPNext 本地开发环境
# 基于源码构建，支持本地开发调试

networks:
  erpnext_network:
    driver: bridge

volumes:
  erpnext_sites:
  erpnext_assets:
  erpnext_logs:
  mariadb_data:
  redis_cache_data:
  redis_queue_data:

services:
  # MariaDB 数据库
  mariadb:
    image: mariadb:10.6
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-123}
      MYSQL_DATABASE: erpnext
      MYSQL_USER: erpnext
      MYSQL_PASSWORD: ${DB_PASSWORD:-123}
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mariadb_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - erpnext_network
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --skip-character-set-client-handshake
      --skip-innodb-read-only-compressed

  # Redis 缓存
  redis-cache:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_cache_data:/data
    networks:
      - erpnext_network

  # Redis 队列
  redis-queue:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_queue_data:/data
    networks:
      - erpnext_network

  # ERPNext 应用构建和运行
  erpnext:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: development
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
        FRAPPE_BRANCH: ${FRAPPE_BRANCH:-version-15}
        ERPNEXT_BRANCH: ${ERPNEXT_BRANCH:-version-15}
    restart: unless-stopped
    environment:
      # 数据库配置
      DB_HOST: mariadb
      DB_PORT: 3306
      DB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-123}
      # Redis 配置
      REDIS_CACHE: redis-cache:6379
      REDIS_QUEUE: redis-queue:6379
      # 站点配置
      SITE_NAME: ${SITE_NAME:-development.localhost}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD:-admin}
      # 开发配置
      DEVELOPER_MODE: ${DEVELOPER_MODE:-1}
      DEBUG_MODE: ${DEBUG_MODE:-1}
      START_MODE: ${START_MODE:-gunicorn}
      # 分支配置
      FRAPPE_BRANCH: ${FRAPPE_BRANCH:-version-15}
      ERPNEXT_BRANCH: ${ERPNEXT_BRANCH:-version-15}
    volumes:
      # 持久化数据
      - erpnext_sites:/home/<USER>/frappe-bench/sites
      - erpnext_assets:/home/<USER>/frappe-bench/sites/assets
      - erpnext_logs:/home/<USER>/frappe-bench/logs
      # 本地源码挂载 (如果存在)
      - ${ERPNEXT_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/erpnext:ro
      - ${FRAPPE_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/frappe:ro
    ports:
      - "${HTTP_PORT:-8080}:8000"
    depends_on:
      - mariadb
      - redis-cache
      - redis-queue
    networks:
      - erpnext_network

  # Nginx 前端代理
  nginx:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: base
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
    restart: unless-stopped
    environment:
      BACKEND: erpnext:8000
      SOCKETIO: websocket:9000
      FRAPPE_SITE_NAME_HEADER: ${SITE_NAME:-development.localhost}
      UPSTREAM_REAL_IP_ADDRESS: 127.0.0.1
      UPSTREAM_REAL_IP_HEADER: X-Forwarded-For
      UPSTREAM_REAL_IP_RECURSIVE: off
      PROXY_READ_TIMEOUT: 120
      CLIENT_MAX_BODY_SIZE: 50m
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites:ro
      - erpnext_assets:/home/<USER>/frappe-bench/sites/assets:ro
    ports:
      - "80:8080"
    depends_on:
      - erpnext
      - websocket
    networks:
      - erpnext_network
    command: nginx-entrypoint.sh

  # WebSocket 服务
  websocket:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: development
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
    restart: unless-stopped
    environment:
      DB_HOST: mariadb
      REDIS_CACHE: redis-cache:6379
      REDIS_QUEUE: redis-queue:6379
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites:ro
      - ${ERPNEXT_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/erpnext:ro
      - ${FRAPPE_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/frappe:ro
    ports:
      - "${SOCKETIO_PORT:-9000}:9000"
    depends_on:
      - mariadb
      - redis-cache
      - redis-queue
    networks:
      - erpnext_network
    command: >
      bash -c "
        source /home/<USER>/frappe-bench/env/bin/activate &&
        node /home/<USER>/frappe-bench/apps/frappe/socketio.js
      "

  # 后台任务队列 - 短任务
  queue-short:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: development
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
    restart: unless-stopped
    environment:
      DB_HOST: mariadb
      REDIS_CACHE: redis-cache:6379
      REDIS_QUEUE: redis-queue:6379
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites
      - ${ERPNEXT_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/erpnext:ro
      - ${FRAPPE_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/frappe:ro
    depends_on:
      - mariadb
      - redis-cache
      - redis-queue
    networks:
      - erpnext_network
    command: >
      bash -c "
        source /home/<USER>/frappe-bench/env/bin/activate &&
        bench worker --queue short,default
      "

  # 后台任务队列 - 长任务
  queue-long:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: development
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
    restart: unless-stopped
    environment:
      DB_HOST: mariadb
      REDIS_CACHE: redis-cache:6379
      REDIS_QUEUE: redis-queue:6379
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites
      - ${ERPNEXT_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/erpnext:ro
      - ${FRAPPE_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/frappe:ro
    depends_on:
      - mariadb
      - redis-cache
      - redis-queue
    networks:
      - erpnext_network
    command: >
      bash -c "
        source /home/<USER>/frappe-bench/env/bin/activate &&
        bench worker --queue long,default,short
      "

  # 定时任务调度器
  scheduler:
    build:
      context: ..
      dockerfile: docker_erpnext/Dockerfile
      target: development
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.11.6}
        NODE_VERSION: ${NODE_VERSION:-20.19.2}
    restart: unless-stopped
    environment:
      DB_HOST: mariadb
      REDIS_CACHE: redis-cache:6379
      REDIS_QUEUE: redis-queue:6379
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites
      - ${ERPNEXT_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/erpnext:ro
      - ${FRAPPE_SOURCE_PATH:-./empty}:/home/<USER>/frappe-bench/apps/frappe:ro
    depends_on:
      - mariadb
      - redis-cache
      - redis-queue
    networks:
      - erpnext_network
    command: >
      bash -c "
        source /home/<USER>/frappe-bench/env/bin/activate &&
        bench schedule
      "
