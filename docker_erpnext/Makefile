# ERPNext 本地开发环境 Makefile

.PHONY: help build up down restart logs shell clean reset

# 默认目标
help:
	@echo "ERPNext 本地开发环境命令:"
	@echo ""
	@echo "  make build    - 构建 Docker 镜像"
	@echo "  make up       - 启动所有服务"
	@echo "  make down     - 停止所有服务"
	@echo "  make restart  - 重启 ERPNext 服务"
	@echo "  make logs     - 查看 ERPNext 日志"
	@echo "  make shell    - 进入 ERPNext 容器"
	@echo "  make clean    - 停止服务并删除容器"
	@echo "  make reset    - 完全重置环境 (删除数据)"
	@echo ""
	@echo "  make status   - 查看服务状态"
	@echo "  make bench    - 进入 bench 命令行"
	@echo "  make migrate  - 运行数据库迁移"
	@echo "  make build-assets - 构建前端资源"
	@echo ""

# 构建镜像
build:
	@echo "构建 Docker 镜像..."
	docker-compose build

# 启动服务
up:
	@echo "启动所有服务..."
	docker-compose up -d
	@echo "等待服务启动..."
	@sleep 10
	@echo "服务状态:"
	@docker-compose ps

# 停止服务
down:
	@echo "停止所有服务..."
	docker-compose down

# 重启 ERPNext 服务
restart:
	@echo "重启 ERPNext 服务..."
	docker-compose restart erpnext

# 查看日志
logs:
	docker-compose logs -f erpnext

# 进入容器
shell:
	docker-compose exec erpnext bash

# 查看服务状态
status:
	docker-compose ps

# 进入 bench 命令行
bench:
	docker-compose exec erpnext bash -c "cd /home/<USER>/frappe-bench && bash"

# 运行数据库迁移
migrate:
	docker-compose exec erpnext bash -c "cd /home/<USER>/frappe-bench && bench migrate"

# 构建前端资源
build-assets:
	docker-compose exec erpnext bash -c "cd /home/<USER>/frappe-bench && bench build"

# 清理容器
clean:
	@echo "停止服务并删除容器..."
	docker-compose down --remove-orphans

# 完全重置环境
reset:
	@echo "⚠️  这将删除所有数据!"
	@read -p "确认要重置环境吗? [y/N]: " confirm && [ "$$confirm" = "y" ]
	@echo "重置环境..."
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "✅ 环境已重置"

# 快速启动 (构建 + 启动)
start: build up
	@echo "🎉 ERPNext 开发环境已启动!"
	@echo "访问地址: http://localhost:8080"
	@echo "默认用户: Administrator"
	@echo "默认密码: admin"
