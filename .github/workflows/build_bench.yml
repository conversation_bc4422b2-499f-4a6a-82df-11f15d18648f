name: Bench

on:
  pull_request:
    branches:
      - main
    paths:
      - images/bench/**
      - docker-bake.hcl
      - .github/workflows/build_bench.yml

  schedule:
    # Every day at 12:00 pm
    - cron: 0 0 * * *

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:latest
          platforms: all

      - name: Setup Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set Environment Variables
        run: cat example.env | grep -o '^[^#]*' >> "$GITHUB_ENV"

      - name: Get Bench Latest Version
        run: echo "LATEST_BENCH_RELEASE=$(curl -s 'https://api.github.com/repos/frappe/bench/releases/latest' | jq -r '.tag_name')" >> "$GITHUB_ENV"

      - name: Build and test
        uses: docker/bake-action@v6.8.0
        with:
          source: .
          targets: bench-test

      - name: Login
        if: ${{ github.repository == 'frappe/frappe_docker' && github.event_name != 'pull_request' }}
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push
        if: ${{ github.repository == 'frappe/frappe_docker' && github.event_name != 'pull_request' }}
        uses: docker/bake-action@v6.8.0
        with:
          targets: bench
          push: true
          set: "*.platform=linux/amd64,linux/arm64"
