---
name: Bug report
about: Report a bug encountered while using the Frappe_docker
labels: bug
---

<!--
Welcome to the frappe_docker issue tracker! Before creating an issue, please heed the following:

1. Is your issue relevant to the frappe_docker or the main Frappe framework? https://github.com/frappe/frappe . if It's the latter, publish the issue there.
2. Use the search function before creating a new issue. Duplicates will be closed and directed to the original discussion.
3. When making a bug report, make sure you provide all the required information. The easier it is for maintainers to reproduce, the faster it'll be fixed.
4. If you think you know what the reason for the bug is, share it with us. Maybe put in a PR 😉
-->

## Description of the issue

## Context information (for bug reports)

## Steps to reproduce the issue

1.
2.
3.

### Observed result

### Expected result

### Stacktrace / full error message if available

```
(paste here)
```
