# ERPNext Docker 镜像构建流程深度分析

## 概述

本文档详细分析了 ERPNext 项目如何通过 GitHub Actions 自动化流水线被打包成 Docker Hub 上的 `frappe/erpnext` 镜像。整个过程基于 [frappe/frappe_docker](https://github.com/frappe/frappe_docker) 项目的自动化构建系统。

## 项目架构关系

### Frappe Framework 与 ERPNext 的关系
- **Frappe Framework**: 是一个基于 Python 的全栈 Web 应用框架，提供了 ORM、用户管理、权限系统等基础功能
- **ERPNext**: 是基于 Frappe Framework 构建的开源 ERP 系统，是 Frappe 框架的一个具体应用
- **Bench**: 是 Frappe 生态系统的命令行工具，用于管理 Frappe 应用的安装、配置和部署

### 项目依赖关系
```
frappe/frappe (Framework) ← frappe/erpnext (Application) ← frappe/frappe_docker (Containerization)
```

## 整体构建流程图

```mermaid
flowchart TD
    A[触发事件] --> B{触发类型}
    B -->|Push/PR to main| C[build_stable.yml]
    B -->|Repository Dispatch| C
    B -->|Manual Trigger| C
    
    C --> D[并行构建任务]
    D --> E[v14 构建任务]
    D --> F[v15 构建任务]
    
    E --> G[调用 docker-build-push.yml]
    F --> H[调用 docker-build-push.yml]
    
    G --> I[多架构构建矩阵]
    H --> I
    I --> J[AMD64 构建]
    I --> K[ARM64 构建]
    
    J --> L[设置 QEMU 模拟器]
    K --> L
    L --> M[设置 Docker Buildx]
    M --> N[获取最新版本标签]
    N --> O[执行 Docker Bake 构建]
    
    O --> P[读取 docker-bake.hcl]
    P --> Q[多阶段 Dockerfile 构建]
    
    Q --> R[Stage 1: base<br/>安装系统依赖]
    R --> S[Stage 2: build<br/>安装编译依赖]
    S --> T[Stage 3: builder<br/>bench init & get-app]
    T --> U[Stage 4: erpnext<br/>最终镜像]
    
    U --> V[推送到本地注册表]
    V --> W[运行测试套件]
    W --> X{测试通过?}
    
    X -->|否| Y[构建失败]
    X -->|是| Z[登录 Docker Hub]
    Z --> AA[推送多架构镜像]
    
    AA --> BB[生成镜像标签]
    BB --> CC[frappe/erpnext:v15.x.x]
    BB --> DD[frappe/erpnext:v15]
    BB --> EE[frappe/erpnext:latest]
    
    F --> FF[更新版本任务]
    FF --> GG[更新 example.env]
    GG --> HH[更新 pwd.yml]
    HH --> II[提交并推送更改]
    
    F --> JJ[Helm 发布任务]
    JJ --> KK[更新 Helm Charts]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style O fill:#fff3e0
    style U fill:#e8f5e8
    style AA fill:#fce4ec
```

## 核心构建文件分析

### 1. GitHub Actions 工作流文件

#### `.github/workflows/build_stable.yml` - 主构建流程
这是触发 ERPNext 镜像构建的主要工作流文件：

**触发条件**:
- **Pull Request**: 当 PR 提交到 `main` 分支且涉及特定路径(paths配置的)时
- **Push**: 推送到 `main` 分支且涉及特定路径(paths配置的)时  
- **Repository Dispatch**: 可配置从 `frappe/frappe` 和 `frappe/erpnext` 仓库的发布事件触发
- **Manual Dispatch**: 可手动触发

**构建任务**:
- **v14 任务**: 构建 ERPNext v14 版本 (Python 3.10.13, Node.js 16.20.2)
- **v15 任务**: 构建 ERPNext v15 版本 (Python 3.11.6, Node.js 20.19.2)
- **版本更新任务**: 自动更新 `example.env` 和 `pwd.yml` 文件
- **Helm 发布任务**: 更新 Helm Charts

#### `.github/workflows/docker-build-push.yml` - 可复用构建模板
这是一个可复用的工作流模板，被 `build_stable.yml` 调用：

**输入参数**:
- `repo`: 指定构建的仓库 (`erpnext` 或 `frappe`)
- `version`: 主版本号或 `develop`
- `push`: 是否推送到 Docker Hub
- `python_version` & `node_version`: 运行时版本

**构建步骤**:
1. **多架构支持设置**: 使用 ubuntu-latest 基础镜像, 使用 QEMU 模拟器支持 ARM64 和 AMD64 架构
2. **Buildx 设置**: 配置 Docker Buildx 构建器
3. **版本获取**: 运行脚本获取最新的 Git 标签
4. **Docker Bake 构建**: 使用 `docker-bake.hcl` 配置文件构建镜像
5. **测试**: 运行 pytest 测试套件验证镜像
6. **推送**: 登录 Docker Hub 并推送多架构镜像

### 2. Docker Bake 配置文件

#### `docker-bake.hcl` - 构建配置定义
这是 Docker Buildx Bake 的 HCL (HashiCorp Configuration Language) 配置文件：

**变量定义**:
```hcl
variable "REGISTRY_USER" { default = "frappe" }
variable "PYTHON_VERSION" { default = "3.11.6" }
variable "NODE_VERSION" { default = "18.18.2" }
variable "FRAPPE_VERSION" { default = "develop" }
variable "ERPNEXT_VERSION" { default = "develop" }
```

**构建目标**:
- **erpnext**: 完整的 ERPNext 应用镜像
- **base**: 基础 Frappe 框架镜像  
- **build**: 构建环境镜像
- **bench**: Bench 工具镜像

**标签生成函数**:
```hcl
function "tag" {
    params = [repo, version]
    result = [
        "${REGISTRY_USER}/${repo}:${version}",
        "${version}" == "develop" ? "${REGISTRY_USER}/${repo}:latest" : "${REGISTRY_USER}/${repo}:${version}",
        # 生成主版本标签，如 v13.16.0 -> v13
        can(regex("(v[0-9]+)[.]", "${version}")) ? "${REGISTRY_USER}/${repo}:${regex("(v[0-9]+)[.]", "${version}")[0]}" : "",
    ]
}
```

### 3. Dockerfile 分析

#### `images/production/Containerfile` - 多阶段构建
这是一个多阶段 Docker 构建文件：

**Stage 1: base** - 基础环境
- 基于 `python:3.11.6-slim-bookworm`
- 安装系统依赖: nginx, git, curl, mariadb-client, postgresql-client 等
- 安装 Node.js (通过 NVM)
- 安装 wkhtmltopdf (用于 PDF 生成)
- 安装 frappe-bench CLI 工具
- 配置 nginx 以非 root 用户运行
- 生成 /templates/nginx/frappe.conf.template 应用访问配置模板
- 写入 nginx 启动脚本: /usr/local/bin/nginx-entrypoint.sh

**Stage 2: build** - 构建环境  
- 继承 base 阶段
- 安装编译依赖: gcc, build-essential, libmariadb-dev 等
- 切换到 frappe 用户

**Stage 3: builder** - 应用构建
- 使用 `bench init` 初始化 Frappe 环境
- 使用 `bench get-app` 获取 ERPNext 应用
- 清理 .git 目录减少镜像大小

**Stage 4: erpnext** - 最终镜像
- 从 base 阶段开始
- 复制构建好的应用代码
- 配置工作目录和卷挂载
- 设置 Gunicorn 启动命令

## 版本管理系统

### 自动版本获取
`.github/scripts/get_latest_tags.py` 脚本负责获取最新版本：

**功能**:
- 从 GitHub 仓库获取符合版本模式的最新 Git 标签
- 支持 Frappe 和 ERPNext 两个仓库
- 使用 `git ls-remote` 命令获取远程标签
- 按版本排序获取最新标签

**版本模式**:
- 开发版本: `develop`
- 稳定版本: `v{major}.{minor}.{patch}` (如 `v15.70.2`)

### 配置文件更新
构建完成后自动更新配置文件：
- `example.env`: 更新 `ERPNEXT_VERSION` 变量
- `pwd.yml`: 更新 Play with Docker 配置

## 多架构构建技术

### QEMU 模拟器
使用 `docker/setup-qemu-action` 设置 QEMU 模拟器：
- 支持在 x86_64 主机上构建 ARM64 镜像
- 使用 `tonistiigi/binfmt:latest` 镜像
- 支持所有平台的交叉编译

### Docker Buildx
使用 `docker/setup-buildx-action` 配置构建器：
- 启用 `docker-container` 驱动
- 支持多平台构建和缓存导出
- 配置网络模式为 `host`

### 构建矩阵
GitHub Actions 使用矩阵策略并行构建：
```yaml
strategy:
  matrix:
    arch: [amd64, arm64]
```

## 测试验证流程

### 本地注册表
构建过程中启动本地 Docker 注册表服务：
```yaml
services:
  registry:
    image: docker.io/registry:2
    ports:
      - 5000:5000
```

### 测试套件
使用 pytest 运行测试：
- 连接性测试: 验证数据库、Redis 连接
- 端点测试: 验证 Web 服务响应
- 功能测试: 验证 ERPNext 核心功能

## 发布流程

### Docker Hub 推送
通过 `docker/login-action` 和 `docker/build-push-action` 推送：
- 使用 GitHub Secrets 存储 Docker Hub 凭据
- 推送多架构镜像清单
- 支持 `linux/amd64` 和 `linux/arm64` 平台

### 标签策略
每次构建生成多个标签：
- 完整版本标签: `frappe/erpnext:v15.70.2`
- 主版本标签: `frappe/erpnext:v15`
- 开发版本标签: `frappe/erpnext:latest` (仅 develop 分支)

## 触发机制

### Repository Dispatch
ERPNext 镜像构建可以被以下事件触发：
- `frappe/frappe` 仓库发布新版本
- `frappe/erpnext` 仓库发布新版本
- 手动触发 (workflow_dispatch)

### 自动化更新
构建完成后自动执行：
- 更新示例配置文件
- 提交并推送更改到主分支
- 触发 Helm Charts 更新

## 技术栈总结

### 核心技术
- **Docker Buildx**: 多平台容器构建
- **GitHub Actions**: CI/CD 自动化
- **HCL (HashiCorp Configuration Language)**: 构建配置
- **QEMU**: 跨架构模拟
- **Python**: 脚本和应用运行时
- **Node.js**: 前端资源构建
- **Nginx**: Web 服务器和反向代理

### 关键工具
- **Bench**: Frappe 应用管理工具
- **Gunicorn**: Python WSGI HTTP 服务器  
- **pytest**: 测试框架
- **Git**: 版本控制和源码获取

## 部署架构

### 服务组件
最终的 ERPNext 部署包含以下服务：
- **backend**: Gunicorn 运行的 Python 应用
- **frontend**: Nginx 反向代理和静态文件服务
- **websocket**: Node.js WebSocket 服务
- **queue-short/long**: 后台任务队列
- **scheduler**: 定时任务调度器
- **configurator**: 初始化配置服务

### 数据持久化
- **sites**: 站点数据和配置
- **assets**: 静态资源文件
- **logs**: 应用日志

这个完整的自动化流程确保了 ERPNext 镜像的高质量、多架构支持和及时更新，为用户提供了可靠的容器化 ERP 解决方案。
